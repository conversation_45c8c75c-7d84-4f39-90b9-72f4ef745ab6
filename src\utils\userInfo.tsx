// import { TOKEN_KEY } from '@/axios/token'
export const prefix = 'h5__'

export interface UserInfo {
  id: string
  token: string
  nickname: string
  userId: string
  username: string
  version?: string
  avatar?: string
  mainOrganizationName?: string
}
export const loginStatus: {
  isLogin: boolean;
  userInfo: UserInfo | null
  usageLog: any[]
} = {
  isLogin: false,
  userInfo: null,
  usageLog: []
}
// 初始化时同步更新 loginStatus
const storedUserInfo = localStorage.getItem(`${prefix}userInfo`)
if (storedUserInfo) {
  const parsedUserInfo = JSON.parse(storedUserInfo)
  loginStatus.userInfo = parsedUserInfo
  loginStatus.isLogin = true
}

export let userInfo = loginStatus.userInfo
export const getUsageLog = () => localStorage.getItem(`${prefix}usageLog`) ? JSON.parse(localStorage.getItem(`${prefix}usageLog`) as any) : loginStatus.usageLog
export const setUserInfo = (v: any) => {
  if (v) {
    const newUserInfo = {
      id: v.id,
      nickname: v.nickname,
      userId: v.userId,
      username: v.username,
      version: v.version,
      avatar: v.avatar,
      token: v.token || loginStatus.userInfo?.token,
      mainOrganizationName: v.mainOrganizationName
    }
    // 同步更新两处状态
    userInfo = newUserInfo
    loginStatus.userInfo = newUserInfo
    loginStatus.isLogin = true
    localStorage.setItem(`${prefix}userInfo`, JSON.stringify(newUserInfo))
  } else {
    // 清空所有相关状态
    userInfo = null
    loginStatus.userInfo = null
    loginStatus.isLogin = false
    localStorage.removeItem(`${prefix}userInfo`)
  }
}
export const getUserInfo = (): UserInfo | null => {
  // 如果内存中有用户信息，直接返回
  if (loginStatus.userInfo) {
    return loginStatus.userInfo
  }

  // 从 localStorage 获取
  try {
    const storedUserInfo = localStorage.getItem(`${prefix}userInfo`)
    if (!storedUserInfo) {
      // 确保状态一致性
      loginStatus.isLogin = false
      loginStatus.userInfo = null
      return null
    }

    // 解析用户信息
    const userInfo = JSON.parse(storedUserInfo) as UserInfo

    // 验证必要字段
    if (!userInfo?.id || !userInfo?.userId || !userInfo?.username || !userInfo?.token) {
      // 数据无效，清除存储和状态
      localStorage.removeItem(`${prefix}userInfo`)
      loginStatus.isLogin = false
      loginStatus.userInfo = null
      return null
    }

    // 更新 loginStatus
    loginStatus.userInfo = userInfo
    loginStatus.isLogin = true

    return userInfo
  } catch (error) {
    console.error('Error getting user info:', error)
    // 发生错误时清除可能损坏的数据
    localStorage.removeItem(`${prefix}userInfo`)
    loginStatus.isLogin = false
    loginStatus.userInfo = null
    return null
  }
}

/**
 * 清除用户信息
 * 1. 清除内存中的用户信息
 * 2. 清除 localStorage 中的用户信息
 * 3. 更新登录状态
 */
export const clearUserInfo = (): void => {
  // 清除内存中的状态
  loginStatus.isLogin = false
  loginStatus.userInfo = null
  
  // 清除 localStorage 中的用户信息
  try {
    // localStorage.removeItem(TOKEN_KEY)
    localStorage.removeItem(`${prefix}userInfo`)
  } catch (error) {
    console.error('Error removing user info from localStorage:', error)
  }
  
  // 如果有其他需要清除的相关数据，也可以在这里处理
  // 例如：清除购物车、历史记录等
  
  // 可以添加额外的清理逻辑，如清除 token
  // try {
  //   localStorage.removeItem(`${prefix}token`)
  // } catch (error) {
  //   console.error('Error removing token from localStorage:', error)
  // }
}