import { useState } from 'react'
import { TabBar } from 'antd-mobile'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  AppOutline,
  FaceRecognitionOutline,
  UnorderedListOutline,
  UserOutline,
} from 'antd-mobile-icons'

export default function BottomNav() {
  const navigate = useNavigate()
  const location = useLocation()
  const tabs = [
    {
      key: '/',
      title: '首页',
      icon: <AppOutline />
    },
    {
      key: '/mall',
      title: '商城',
      icon: <FaceRecognitionOutline />
    },
    {
      key: '/app',
      title: '应用',
      icon: <UnorderedListOutline />
    },
    {
      key: '/my',
      title: '我的',
      icon: <UserOutline />
    }
  ]
  const [activeKey, setActiveKey] = useState(location.pathname)
  
  const handleChange = (value: string) => {
    setActiveKey(value)
    navigate(value)
  }

  return (
    <TabBar activeKey={activeKey} onChange={handleChange}>
      {
        tabs.map(item => (
          <TabBar.Item key={item.key} icon={item.icon} title={item.title}/>
        ))
      }
    </TabBar>
  )
}
