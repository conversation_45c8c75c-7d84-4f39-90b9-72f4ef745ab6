import { Outlet, useLocation, Navigate } from 'react-router-dom';
import BottomNav from '@/components/BottomNav';
import { setUserInfo, getUserInfo } from '@/utils/userInfo';

const showNavPath = ['/', '/app', '/mall', '/my']
export default function Layout () {
  const location = useLocation();
  const shouldShowNav = showNavPath.includes(location.pathname);
  return (
    <div>
      <main className='mainPage'>
        {/* {getUserInfo() ? <Outlet /> : <Navigate to={'/login'} />} */}
        <Outlet />
      </main>
      {shouldShowNav && <BottomNav />}
    </div>
  );
}