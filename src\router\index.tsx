import { createHashRouter } from "react-router-dom";
import { lazyLoad } from "@/utils/LazyLoad";
import Layout from "@/layout";

export const router = createHashRouter([
  {
    path: "/login",
    element: lazyLoad(() => import("@/pages/login")),
  },
  {
    element: <Layout />,
    children: [
      {
        path: "/",
        element: lazyLoad(() => import("@/pages/home")),
      },
      {
        path: "/my",
        element: lazyLoad(() => import("@/pages/my")),
      },
      {
        path: "/mall",
        element: lazyLoad(() => import("@/pages/mall")),
      },
      {
        path: "/app",
        element: lazyLoad(() => import("@/pages/app")),
      },
      {
        path: "/apply",
        element: lazyLoad(() => import("@/pages/apply")),
      },
    ]
  }
]);
